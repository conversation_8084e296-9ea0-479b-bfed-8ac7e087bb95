'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/dropdown-menu'
import { Badge } from '@/components/badge'
import { Building2, Check, ChevronDown } from 'lucide-react'
import { useBranch } from '@/contexts/branch-context'

export function BranchSwitcher() {
  const { data: session } = useSession()
  const { currentBranch, branches, switchBranch, isLoading } = useBranch()
  const [isOpen, setIsOpen] = useState(false)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Only show branch switcher for ADMIN users
  const userRole = (session?.user as any)?.role
  if (userRole !== 'ADMIN') {
    return null
  }

  if (!mounted) {
    return null
  }

  if (isLoading) {
    return (
      <div className="flex items-center gap-3 px-4 py-2 bg-muted/50 rounded-lg border">
        <Building2 className="h-4 w-4 text-muted-foreground animate-pulse" />
        <span className="text-sm text-muted-foreground">Loading branches...</span>
      </div>
    )
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="flex items-center gap-3 min-w-[240px] justify-between bg-background/95 backdrop-blur-sm border-border hover:bg-accent hover:shadow-sm transition-all duration-200 rounded-lg h-10"
        >
          <div className="flex items-center gap-3">
            <div className="h-7 w-7 rounded-md bg-primary/10 flex items-center justify-center">
              <Building2 className="h-4 w-4 text-primary" />
            </div>
            <div className="flex flex-col items-start">
              <span className="font-medium text-foreground text-sm">{currentBranch.name}</span>
              <Badge variant="secondary" className="text-xs font-medium h-4 px-1.5">
                Active
              </Badge>
            </div>
          </div>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[280px] rounded-lg border shadow-lg z-[100]">
        <DropdownMenuLabel className="text-sm font-semibold px-4 py-3">Switch Branch</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {branches.map((branch) => (
          <DropdownMenuItem
            key={branch.id}
            onClick={() => {
              switchBranch(branch.id)
              setIsOpen(false)
            }}
            className="flex items-center justify-between cursor-pointer px-4 py-3 hover:bg-accent transition-colors duration-200"
          >
            <div className="flex flex-col">
              <div className="flex items-center gap-3">
                <div className="h-6 w-6 rounded-md bg-muted flex items-center justify-center">
                  <Building2 className="h-3.5 w-3.5 text-muted-foreground" />
                </div>
                <span className="font-medium">{branch.name}</span>
                {currentBranch.id === branch.id && (
                  <Check className="h-4 w-4 text-primary" />
                )}
              </div>
              {branch.address && (
                <span className="text-xs text-muted-foreground ml-9 mt-0.5">
                  {branch.address}
                </span>
              )}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
