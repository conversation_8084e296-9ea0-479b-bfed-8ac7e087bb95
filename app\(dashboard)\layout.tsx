"use client"

import { AppSidebar } from '@/components/app-sidebar'
import { Toaster } from '@/components/ui/toaster'
import { BranchProvider } from '@/contexts/branch-context'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/breadcrumb"
import { Separator } from "@/components/separator"
import UserDropdown from "@/components/user-dropdown"
import FeedbackDialog from "@/components/feedback-dialog"
import { ThemeToggle } from "@/components/theme-toggle"
import { BranchSwitcher } from "@/components/ui/branch-switcher"


export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {

  return (
    <BranchProvider>
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset className="overflow-x-hidden">
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-50">
            <div className="flex flex-1 items-center gap-2">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/dashboard">
                      Dashboard
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Current Page</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>



            {/* Branch Switcher for Admin Users */}
            <div className="flex items-center">
              <BranchSwitcher />
            </div>

            <div className="flex gap-2 ml-auto">
              <ThemeToggle />
              <FeedbackDialog />
              <UserDropdown />
            </div>
          </header>
          <main className="flex-1 overflow-x-hidden overflow-y-auto p-4 md:p-6 lg:p-8">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </main>
        </SidebarInset>

        <Toaster />
      </SidebarProvider>
    </BranchProvider>
  )
}
