import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '12months'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case '3months':
        startDate.setMonth(now.getMonth() - 3)
        break
      case '6months':
        startDate.setMonth(now.getMonth() - 6)
        break
      case '12months':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      case '24months':
        startDate.setFullYear(now.getFullYear() - 2)
        break
      default:
        startDate.setFullYear(now.getFullYear() - 1)
    }

    // Generate month array for the range
    const months = []
    const current = new Date(startDate)
    while (current <= now) {
      months.push({
        month: current.toLocaleDateString('en-US', { month: 'short' }),
        year: current.getFullYear(),
        monthStart: new Date(current.getFullYear(), current.getMonth(), 1),
        monthEnd: new Date(current.getFullYear(), current.getMonth() + 1, 0, 23, 59, 59)
      })
      current.setMonth(current.getMonth() + 1)
    }

    // Get monthly revenue data
    const monthlyRevenue = []
    for (const month of months) {
      const payments = await prisma.payment.aggregate({
        where: {
          status: 'PAID',
          paidDate: {
            gte: month.monthStart,
            lte: month.monthEnd,
          },
        },
        _sum: { amount: true },
        _count: { id: true },
      })

      const revenue = Number(payments._sum.amount) || 0
      const paymentCount = payments._count.id || 0

      // Calculate target (could be configurable in the future)
      const target = Math.max(revenue * 1.1, 50000000) // 10% higher than actual or minimum 50M

      monthlyRevenue.push({
        month: month.month,
        revenue,
        payments: paymentCount,
        target,
      })
    }

    // Get payment methods breakdown
    const paymentMethods = await prisma.payment.groupBy({
      by: ['method'],
      where: {
        status: 'PAID',
        paidDate: {
          gte: startDate,
          lte: now,
        },
      },
      _sum: { amount: true },
    })

    const totalRevenue = paymentMethods.reduce((sum, method) => sum + (Number(method._sum.amount) || 0), 0)
    
    const paymentMethodData = paymentMethods.map(method => ({
      method: method.method,
      amount: Number(method._sum.amount) || 0,
      percentage: totalRevenue > 0 ? Math.round(((Number(method._sum.amount) || 0) / totalRevenue) * 100) : 0,
    }))

    // Get recent large payments for insights
    const recentLargePayments = await prisma.payment.findMany({
      where: {
        status: 'PAID',
        amount: { gte: 1000000 }, // Payments over 1M UZS
        paidDate: {
          gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
        },
      },
      include: {
        student: {
          select: { name: true }
        }
      },
      orderBy: { paidDate: 'desc' },
      take: 10,
    })

    // Calculate growth metrics
    const currentMonthRevenue = monthlyRevenue[monthlyRevenue.length - 1]?.revenue || 0
    const previousMonthRevenue = monthlyRevenue[monthlyRevenue.length - 2]?.revenue || 0
    const growthRate = previousMonthRevenue > 0 
      ? Math.round(((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100)
      : 0

    const response = {
      monthlyRevenue,
      paymentMethods: paymentMethodData,
      summary: {
        totalRevenue,
        currentMonthRevenue,
        previousMonthRevenue,
        growthRate,
        totalPayments: monthlyRevenue.reduce((sum, month) => sum + month.payments, 0),
        averagePaymentSize: totalRevenue > 0 ? Math.round(totalRevenue / monthlyRevenue.reduce((sum, month) => sum + month.payments, 0)) : 0,
      },
      recentLargePayments: recentLargePayments.map(payment => ({
        id: payment.id,
        amount: Number(payment.amount),
        studentName: payment.student?.user?.name || 'Unknown',
        method: payment.method,
        paidDate: payment.paidDate,
      })),
      dateRange: {
        start: startDate.toISOString(),
        end: now.toISOString(),
        range,
      },
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error fetching revenue analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
